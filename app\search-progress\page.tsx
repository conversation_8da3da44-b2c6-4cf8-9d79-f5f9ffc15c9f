"use client";

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Search, MapPin, Calendar as CalendarIcon, Users, Briefcase, ArrowLeft, ArrowRight, CheckCircle2, Sparkles, XCircle, Users2, UsersRoundIcon } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { Calendar } from '@/components/ui/calendar';
import { format } from 'date-fns';
import { useDispatch } from 'react-redux';
import { changeDate, changeDestination, changeDestinationId } from '@/app/store/features/searchPackageSlice';
import { selectAdultsChild } from '@/app/store/features/roomCapacitySlice';
import { selectTheme, selectThemeId } from '@/app/store/features/selectThemeSlice';
import { useQuery } from 'react-query';
import { getInterest } from '@/app/actions/get-interest';
import { NEXT_PUBLIC_IMAGE_URL } from '@/app/utils/constants/apiUrls';
import { useMediaQuery } from 'react-responsive';

// Define types for booking data
interface BookingData {
    destination: string;
    destinationId: string;
    date: Date | undefined;
    rooms: number;
    adults: number;
    children: number;
    theme: string;
    themeId: string;
    departureCity?: string;
    tripType?: string;
    duration?: string;
}

// Define Interest interface
interface Interest {
    image: string;
    interestId: string;
    interestName: string;
    isFirst: boolean;
    sort: number;
    _id: string;
}

// Booking Progress Component with dynamic labels and navigation
const BookingProgress = ({ currentStep, bookingData, onNavigate }: { currentStep: number; bookingData: BookingData; onNavigate?: (step: number) => void }) => {
  const steps = [
    { label: "Destination", icon: MapPin },
    { label: "Date", icon: CalendarIcon },
    { label: "Rooms & Travelers", icon: Users },
  ];
  const isMobile = useMediaQuery({ query: '(max-width: 768px)' });

  return (
    <div className="bg-white/80 backdrop-blur-sm p-2 md:p-3 rounded-full shadow-md border border-gray-200/70 w-full md:w-auto">
      <div className="flex items-center justify-between md:justify-center space-x-1 md:space-x-3 text-xs md:text-base w-full">
        {steps.map((step, index) => (
          <React.Fragment key={index}>
            <button
              type="button"
              onClick={() => onNavigate && index <= currentStep && onNavigate(index)}
              disabled={index > currentStep}
              className={cn(
                "flex items-center font-semibold transition-colors duration-300 focus:outline-none text-center",
                currentStep === index ? "text-gray-900 font-bold" : "text-gray-400",
                index < currentStep ? "text-[#23cd92] cursor-pointer" : "cursor-default"
              )}
            >
              <step.icon className="hidden md:inline-block w-4 h-4 md:mr-2 shrink-0" />
              <span className="sm:ml-2">
                {index === 0 && (bookingData.destination ? bookingData.destination : "Destination")}
                {index === 1 && (bookingData.date ? (isMobile ? format(bookingData.date, "do MMM") : format(bookingData.date, "PPP")) : "Date")}
                {index === 2 && (isMobile ? "Travelers" : "Rooms & Travelers")}
              </span>
            </button>
            {index < steps.length - 1 && (
              <div className="h-4 w-px bg-gray-300 mx-1 md:mx-2"></div>
            )}
          </React.Fragment>
        ))}
      </div>
    </div>
  );
};

// Page Layout Template (with bottom-right CTA and dynamic progress)
const PageLayout = ({ children, currentStep, title, leftButton, bottomRightButton, backgroundImage, bookingData, onNavigate }: { children: React.ReactNode; currentStep: number; title: string; leftButton?: React.ReactNode; bottomRightButton?: React.ReactNode; backgroundImage?: string; bookingData: BookingData; onNavigate?: (step: number) => void }) => (
  <div
    className="min-h-screen bg-cover bg-center"
    style={{
      backgroundImage: backgroundImage ? `url(${backgroundImage})` : 'none',
    }}
  >
    <div className="min-h-screen bg-white bg-opacity-95 relative">
      <main className="container mx-auto px-4 py-8 md:py-12">
        {/* Progress Indicator and Navigation */}
        <div className="mb-8 md:mb-12 w-full">
          <div className="flex items-center justify-between gap-4">
            <div className="flex-shrink-0">{leftButton}</div>
            <div className="flex-1 max-w-3xl mx-4">
              <BookingProgress currentStep={currentStep} bookingData={bookingData} onNavigate={onNavigate} />
            </div>
            <div className="flex-shrink-0 w-[120px] hidden md:block" />
          </div>
        </div>

        {/* Page Header */}
        <div className="text-center mb-4 md:mb-16">
          <h1 className="text-lg lg:text-4xl sm:text-xl font-bold bg-gradient-to-r from-gray-800 via-gray-700 to-gray-800 bg-clip-text text-transparent tracking-tight mb-3 md:mb-4">
            {title}
          </h1>
        </div>

        {/* Page Content */}
        {children}

        {/* Bottom Right CTA */}
      {bottomRightButton && (
        <div className="fixed bottom-4 left-4 right-4 md:bottom-24 md:right-40 md:left-auto md:transform-none z-50 hidden md:block">
          {bottomRightButton}
        </div>
      )}
      </main>
    </div>
  </div>
);

// Define Destination interface
interface Destination {
  destinationId: string;
  destinationName: string;
  image?: string;
  popular?: boolean;
  imageError?: boolean;
}

// 1. Destination Selection
const DestinationSelection = ({ onContinue, bookingData, onNavigate, setBookingData }: { onContinue: (destination: string, destinationId: string) => void; bookingData: BookingData; onNavigate: (step: number) => void; setBookingData: React.Dispatch<React.SetStateAction<BookingData>> }) => {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [destinationSearch, setDestinationSearch] = useState('');
  const [showDestinationPicker, setShowDestinationPicker] = useState(false);
  const [selectedDestination, setSelectedDestination] = useState<string | null>(bookingData.destination);
  const [selectedDestinationId, setSelectedDestinationId] = useState<string | null>(bookingData.destinationId);
  const [allDestinations, setAllDestinations] = useState<Destination[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const isMobile = useMediaQuery({ query: '(max-width: 768px)' });

  // Fetch destinations from API
  useEffect(() => {
    const fetchDestinations = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('https://api.tripxplo.com/v1/api/user/package/destination/search');
        const data = await response.json();
        if (data.result) {
          // Add some popular destinations with images
          const destinationsWithImages = data.result.map((dest: any) => ({
            ...dest,
            image: getDestinationImage(dest.destinationName, dest.image),
            popular: isPopularDestination(dest.destinationName),
            imageError: false
          }));
          setAllDestinations(destinationsWithImages);
        }
      } catch (error) {
        console.error('Error fetching destinations:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchDestinations();
  }, []);

  // Fetch filtered destinations when search query changes
  useEffect(() => {
    const fetchFilteredDestinations = async () => {
      try {
        setIsLoading(true);
        let url = 'https://api.tripxplo.com/v1/api/user/package/destination/search';
        
        if (searchQuery.trim() !== '') {
          url += `?search=${encodeURIComponent(searchQuery)}`;
        }

        const response = await fetch(url);
        const data = await response.json();
        if (data.result) {
          // Add some popular destinations with images
          const destinationsWithImages = data.result.map((dest: any) => ({
            ...dest,
            image: getDestinationImage(dest.destinationName, dest.image),
            popular: isPopularDestination(dest.destinationName),
            imageError: false
          }));
          setAllDestinations(destinationsWithImages);
        }
      } catch (error) {
        console.error('Error fetching filtered destinations:', error);
      } finally {
        setIsLoading(false);
      }
    };

    // Debounce the search to avoid too many API calls
    const timeoutId = setTimeout(() => {
      fetchFilteredDestinations();
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchQuery]);

  // Helper function to get destination images
  const getDestinationImage = (name: string, apiImage?: string) => {
    // First priority: API image
    if (apiImage) {
      return NEXT_PUBLIC_IMAGE_URL + apiImage;
    }
    
    // Second priority: Local image fallback
    const imageMap: { [key: string]: string } = {
      'Bali': '/bali.jpg',
      'Goa': '/goa.jpg',
      'Kashmir': '/kashmir.jpg',
      'Manali': '/manali.jpg',
      'Kodaikanal': '/kodaikanal.jpg',
      'Maldives': '/maldives.jpg',
      'Meghalaya': '/meghalaya.jpg',
      'Ooty': '/ooty.jpg',
      'Varkala': '/Varkala.jpg',
    };
    
    if (imageMap[name]) {
      return imageMap[name];
    }
    
    // Third priority: No image available, return null for text-only display
    return null;
  };

  // Helper function to determine popular destinations
  const isPopularDestination = (name: string) => {
    const popularNames = ['Bali', 'Goa', 'Manali', 'Varkala', 'Kashmir', 'Kerala'];
    return popularNames.some(popular => name.toLowerCase().includes(popular.toLowerCase()));
  };

  const handleSelectDestination = (destination: Destination) => {
    setSelectedDestination(destination.destinationName);
    setSelectedDestinationId(destination.destinationId);
    if (isMobile) {
      onContinue(destination.destinationName, destination.destinationId);
    }
  };

  const handleImageError = (destinationId: string) => {
    setAllDestinations(prevDestinations =>
      prevDestinations.map(dest =>
        dest.destinationId === destinationId ? { ...dest, imageError: true } : dest
      )
    );
  };

  const FEATURED_DESTINATIONS = [
    { name: 'Goa', tag: 'POPULAR', color: 'bg-purple-100 text-purple-800', isDomestic: true },
    { name: 'Kashmir', tag: 'HONEYMOON', color: 'bg-pink-100 text-pink-800', isDomestic: true },
    { name: 'Manali', tag:'HONEYMOON', color: 'bg-green-100 text-green-800', isDomestic: true },
    { name: 'Ooty', tag: 'BUDGET', color: 'bg-orange-100 text-orange-800', isDomestic: true },
    { name: 'Munnar', tag: 'TRENDING', color: 'bg-blue-100 text-blue-800', isDomestic: true },
    { name: 'Andaman', tag: 'IN SEASON', color: 'bg-green-100 text-green-800', isDomestic: true },
    { name: 'Kodaikanal', tag: 'IN SEASON', color: 'bg-orange-100 text-orange-800', isDomestic: true },
    { name: 'Coorg', tag: 'BUDGET', color: 'bg-blue-100 text-blue-800', isDomestic: true },
    { name: 'Alleppey', tag: 'BACKWATERS', color: 'bg-green-100 text-green-800', isDomestic: true },
    { name: 'Kochi', tag: 'BUDGET', color: 'bg-blue-100 text-blue-800', isDomestic: true },
    { name: 'Shimla', tag: 'BUDGET', color: 'bg-orange-100 text-orange-800', isDomestic: true },
    { name: 'Yelagiri', tag: '', color: '', isDomestic: true },
    { name: 'Wayanad', tag: '', color: '', isDomestic: true },
    { name: 'Meghalaya', tag: '', color: '', isDomestic: true },
    { name: 'Darjeeling', tag: '', color: '', isDomestic: true },
    { name: 'Sikkim', tag: '', color: '', isDomestic: true },
    { name: 'Delhi', tag: '', color: '', isDomestic: true },
    { name: 'Agra', tag: '', color: '', isDomestic: true },
    { name: 'Pondicherry', tag: '', color: '', isDomestic: true },
    { name: 'Madurai', tag: '', color: '', isDomestic: true },
    { name: 'Rameswaram', tag: '', color: '', isDomestic: true },
    { name: 'Ladakh', tag: '', color: '', isDomestic: true },
    { name: 'Mysore', tag: '', color: '', isDomestic: true },
    // International destinations second
    { name: 'Bali', tag: 'HONEYMOON', color: 'bg-green-100 text-green-800', isDomestic: false },
    { name: 'Maldives', tag: 'HONEYMOON', color: 'bg-pink-100 text-pink-800', isDomestic: false },
    { name: 'Europe', tag: 'IN SEASON', color: 'bg-blue-100 text-blue-800', isDomestic: false },
    { name: 'Thailand', tag: 'BUDGET', color: 'bg-orange-100 text-orange-800', isDomestic: false },
    { name: 'Singapore', tag: 'FAMILY', color: 'bg-red-100 text-red-800', isDomestic: false },
    { name: 'Abu Dhabi', tag: 'POPULAR', color: 'bg-purple-100 text-purple-800', isDomestic: false },
    { name: 'Vietnam', tag: '', color: '', isDomestic: false },
    { name: 'Dubai', tag: '', color: '', isDomestic: false },
    { name: 'Australia', tag: '', color: '', isDomestic: false },
  ];

  const sortedDestinations = [...allDestinations]
    .filter(dest => dest.image && !dest.imageError)
    .sort((a, b) => {
      const aImage = a.image && !a.imageError;
      const bImage = b.image && !b.imageError;
      if (aImage && !bImage) return -1;
      if (!aImage && bImage) return 1;
      return 0;
    });

  return (
    <PageLayout
      currentStep={0}
      title="Where do you wanna travel?"
      backgroundImage="/1976998.jpg"
      leftButton={
        <Button variant="outline" className="w-auto px-3 py-3 text-base md:px-8 md:py-4 md:text-lg bg-white border-2 border-gray-200 rounded-full shadow-md transition-all duration-300 transform md:hover:bg-gray-50 md:hover:border-gray-300 md:hover:shadow-lg md:hover:scale-105" onClick={() => router.push('/')}>
          <ArrowLeft className="w-5 h-5" />
          <span className="hidden md:inline md:ml-2">Back</span>
        </Button>
      }
      bottomRightButton={
        <Button
          className="w-full md:w-auto px-6 py-3 text-base md:px-10 md:py-6 md:text-2xl bg-[#ff7865] hover:bg-[#ff6347] text-white rounded-full shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 relative overflow-hidden group"
          onClick={() => selectedDestination && selectedDestinationId && onContinue(selectedDestination, selectedDestinationId)}
          disabled={!selectedDestination}
        >
          <span className="relative flex items-center justify-center">
            Continue
            <ArrowRight className="w-5 h-5 ml-2 transition-transform group-hover:translate-x-1" />
          </span>
        </Button>
      }
      bookingData={bookingData}
      onNavigate={onNavigate}
    >
      <div className="max-w-4xl mx-auto relative">
        {/* Search Bar */}
        <div className="relative group mb-8">
          <div className="relative bg-white/95 backdrop-blur-sm rounded-full p-1.5 md:p-2 shadow-2xl">
            <div className="flex items-center">
              <Search className="ml-3 md:ml-6 text-gray-400 w-5 h-5 md:w-6 md:h-6" />
              <Input
                placeholder="Search destinations..."
                className="flex-1 border-0 bg-transparent px-3 py-3 text-base md:px-4 md:py-4 md:text-lg placeholder:text-gray-500 focus:ring-0 focus:ring-offset-0 focus:border-transparent focus-visible:ring-0 focus-visible:ring-offset-0"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onFocus={() => isMobile && setShowDestinationPicker(true)}
              />
              {searchQuery && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSearchQuery('')}
                  className="mr-1.5 md:mr-2 rounded-full"
                >
                  <XCircle className="w-5 h-5 text-gray-500" />
                </Button>
              )}
            </div>
          </div>
          {isMobile && showDestinationPicker && (
            <div className="absolute top-full left-0 right-0 bg-white rounded-2xl shadow-2xl mt-2 z-50 p-4 border max-h-96 overflow-y-auto">
               {/* Domestic Section */}
                {FEATURED_DESTINATIONS.filter(dest => 
                        dest.isDomestic && 
                        dest.name.toLowerCase().includes(searchQuery.toLowerCase())
                      ).length > 0 && (
                        <div className="mb-4">
                          <h4 className="text-sm font-bold text-blue-600 mb-2 px-3 py-2 bg-blue-50">🇮🇳 DOMESTIC DESTINATIONS</h4>
                          <div className="space-y-1">
                            {FEATURED_DESTINATIONS.filter(dest => 
                              dest.isDomestic && 
                              dest.name.toLowerCase().includes(searchQuery.toLowerCase())
                            ).map((dest, index) => (
                              <div
                                key={`domestic-${index}`}
                                onClick={() => {
                                  const selectedDest = allDestinations.find(d => d.destinationName === dest.name);
                                  if (selectedDest) {
                                    onContinue(selectedDest.destinationName, selectedDest.destinationId);
                                  }
                                  setShowDestinationPicker(false);
                                  setDestinationSearch('');
                                }}
                                className="flex items-center justify-between p-3 hover:bg-gray-50 cursor-pointer transition-colors group"
                              >
                                <span className="text-gray-800 font-medium group-hover:text-gray-900">
                                  {dest.name}
                                </span>
                                {dest.tag && (
                                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${dest.color}`}>
                                    {dest.tag}
                                  </span>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                      
                      {/* International Section */}
                      {FEATURED_DESTINATIONS.filter(dest => 
                        !dest.isDomestic && 
                        dest.name.toLowerCase().includes(searchQuery.toLowerCase())
                      ).length > 0 && (
                        <div className="border-t pt-4">
                          <h4 className="text-sm font-bold text-green-600 mb-2 px-3 py-2 bg-green-50">🌍 INTERNATIONAL DESTINATIONS</h4>
                          <div className="space-y-1">
                            {FEATURED_DESTINATIONS.filter(dest => 
                              !dest.isDomestic && 
                              dest.name.toLowerCase().includes(searchQuery.toLowerCase())
                            ).map((dest, index) => (
                              <div
                                key={`international-${index}`}
                                onClick={() => {
                                  const selectedDest = allDestinations.find(d => d.destinationName === dest.name);
                                  if (selectedDest) {
                                    onContinue(selectedDest.destinationName, selectedDest.destinationId);
                                  }
                                  setShowDestinationPicker(false);
                                  setDestinationSearch('');
                                }}
                                className="flex items-center justify-between p-3 hover:bg-gray-50 cursor-pointer transition-colors group"
                              >
                                <span className="text-gray-800 font-medium group-hover:text-gray-900">
                                  {dest.name}
                                </span>
                                {dest.tag && (
                                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${dest.color}`}>
                                    {dest.tag}
                                  </span>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
            </div>
          )}
        </div>

        {/* Destination Cards */}
        <div className={cn("space-y-12", isMobile && showDestinationPicker && "hidden")}>
          {isLoading ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {[...Array(8)].map((_, index) => (
                <div key={index} className="w-full animate-pulse h-48 bg-slate-200 rounded-2xl"></div>
              ))}
            </div>
          ) : sortedDestinations.length > 0 ? (
            <div>
              <h2 className="lg:text-2xl sm:text-lg font-bold text-gray-800 mb-6">{searchQuery ? 'Search Results' : 'Popular Destinations'}</h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6">
                {sortedDestinations.map((dest) => (
                  <Card
                    key={dest.destinationId}
                    className={cn(
                      'cursor-pointer transition-all duration-300 rounded-2xl overflow-hidden group border-2 shadow-lg hover:shadow-2xl',
                      selectedDestination === dest.destinationName ? 'border-[#23cd92] shadow-xl ring-4 ring-[#23cd92]/20' : 'border-transparent hover:border-[#23cd92]/30'
                    )}
                    onClick={() => handleSelectDestination(dest)}
                  >
                    <CardContent className="p-0 relative">
                      {dest.popular && (
                        <div className="absolute top-2 left-2 bg-[#ff7865] text-white text-[10px] md:text-xs font-bold px-2 py-0.5 md:py-1 rounded-full z-10">
                          Trending
                        </div>
                      )}
                      <div className="absolute inset-0 bg-gradient-to-br from-[#ff7865]/5 via-transparent to-[#23cd92]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      {selectedDestination === dest.destinationName && (
                        <div className={cn(
                          "absolute w-7 h-7 md:w-8 md:h-8 bg-[#23cd92] rounded-full flex items-center justify-center shadow-lg z-10",
                          dest.image ? "top-2 right-2" : "top-2 right-2"
                        )}>
                          <CheckCircle2 className="w-4 h-4 md:w-5 md:h-5 text-white" />
                        </div>
                      )}
                      {dest.image && !dest.imageError ? (
                        <>
                          <img 
                            src={dest.image} 
                            alt={dest.destinationName} 
                            className="w-full h-28 md:h-40 object-cover" 
                            onError={() => handleImageError(dest.destinationId)}
                          />
                          <div className="p-3 md:p-4 relative z-10">
                            <h3 className="text-base md:text-lg font-semibold text-gray-800">{dest.destinationName}</h3>
                          </div>
                        </>
                      ) : (
                        <div className="p-5 md:p-6 flex items-center justify-center h-28 md:h-40 bg-gradient-to-br from-gray-50 to-gray-100">
                          <div className="text-center">
                            <MapPin className="w-7 h-7 md:w-8 md:h-8 text-gray-400 mx-auto mb-2" />
                            <h3 className="text-base md:text-lg font-semibold text-gray-800">{dest.destinationName}</h3>
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          ) : (
            <div className="text-center py-16 bg-white/80 rounded-2xl shadow-lg">
              <MapPin className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-lg font-medium text-gray-900">No destinations found</h3>
              <p className="mt-1 text-sm text-gray-500">
                We couldn't find any destinations matching your search. Try something else.
              </p>
            </div>
          )}
        </div>
      </div>
    </PageLayout>
  );
};

// 2. Departure Date Page
const DepartureDate = ({ onContinue, onBack, selectedDate, bookingData, onNavigate }: { onContinue: (date: Date) => void; onBack: () => void; selectedDate: Date | undefined; bookingData: BookingData; onNavigate: (step: number) => void }) => {
  const [date, setDate] = useState<Date | undefined>(selectedDate);
  const isMobile = useMediaQuery({ query: '(max-width: 768px)' });

  const handleSelectDate = (newDate: Date | undefined) => {
    setDate(newDate);
    if (isMobile && newDate) {
      onContinue(newDate);
    }
  };

  return (
    <PageLayout
      currentStep={1}
      title="When do you want to travel?"
      backgroundImage="/1976998.jpg"
      leftButton={
        <Button variant="outline" className="w-auto px-3 py-3 text-base md:px-8 md:py-4 md:text-lg bg-white border-2 border-gray-200 rounded-full shadow-md transition-all duration-300 transform md:hover:bg-gray-50 md:hover:border-gray-300 md:hover:shadow-lg md:hover:scale-105" onClick={onBack}>
          <ArrowLeft className="w-5 h-5" />
          <span className="hidden md:inline md:ml-2">Back</span>
        </Button>
      }
      bottomRightButton={
        <Button
          className="w-full md:w-auto px-6 py-3 text-base md:px-10 md:py-6 md:text-2xl bg-[#ff7865] hover:bg-[#ff6347] text-white rounded-full shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 relative overflow-hidden group"
          onClick={() => date && onContinue(date)}
          disabled={!date}
        >
          <span className="relative flex items-center justify-center">
            Continue
            <ArrowRight className="w-5 h-5 ml-2 transition-transform group-hover:translate-x-1" />
          </span>
        </Button>
      }
      bookingData={bookingData}
      onNavigate={onNavigate}
    >
      <div className="max-w-2xl mx-auto">
        <div className="flex justify-center mb-8">
          <Calendar
  mode="single"
  selected={date}
  onSelect={handleSelectDate}
  initialFocus
  disabled={(date) => date < new Date() || date < new Date("1900-01-01")}
  className="w-full md:rounded-2xl md:border-2 md:border-gray-200/80 md:shadow-xl md:p-4"
  classNames={{
    table: "w-full",
    head_cell: "w-full md:w-[60px] font-semibold text-sm md:text-base text-[#333]",
    cell: "w-full h-9 md:w-[60px] md:h-[60px]",
    day: "rounded-full w-full h-full text-xs md:text-base",
    day_selected: "bg-app-primary text-white rounded-t-2xl rounded-b-2xl shadow-[0_4px_12px_rgba(255,120,101,0.4)]",
    day_today: "bg-[#23cd92] text-white",
    day_disabled: "opacity-50",
    caption_label: "text-base md:text-[1.4rem] font-bold text-[#222]",
    nav_button: "rounded-full w-7 h-7 md:w-[40px] md:h-[40px]",
  }}
/>
        </div>

        {date && (
          <div className="w-full flex justify-center mb-6 md:mb-8 px-4">
            <div className="inline-block text-base md:text-lg text-gray-700 transition-all duration-300 ease-in-out transform hover:scale-105">
              <span className="font-semibold text-white bg-[#23cd92] py-1.5 px-3 md:py-2 md:px-4 rounded-full shadow-lg">
                Selected Date: {format(date, "PPP")}
              </span>
            </div>
          </div>
        )}
      </div>
    </PageLayout>
  );
};

// 3. Rooms & Travelers Page
const Counter = ({ value, onValueChange, min = 1, max = 20, icon: Icon, label, selectedValue }: { value: number, onValueChange: (newValue: number) => void, min?: number, max?: number, icon: React.ElementType, label: string, selectedValue?: string }) => (
  <div className="bg-white/80 backdrop-blur-sm border border-gray-200/90 rounded-2xl p-3 md:p-4 shadow-lg hover:shadow-xl transition-shadow duration-300 h-full flex flex-col justify-between">
    <div>
      <div className="flex items-center text-gray-700 mb-3">
        <Icon className="w-5 h-5 mr-2 text-[#ff7865]" />
        <h3 className="text-lg font-semibold">{label}</h3>
      </div>
      {selectedValue && (
        <div className="text-left text-xs text-gray-500 mb-3">
          {selectedValue}
        </div>
      )}
    </div>
    <div className="flex items-center justify-between bg-gray-100/80 rounded-full p-0.5 md:p-1">
      <Button
        variant="ghost"
        size="icon"
        className="w-10 h-10 rounded-full bg-white shadow-md text-lg text-gray-700 hover:bg-gray-50 disabled:opacity-50 transition-all duration-200 transform hover:scale-105"
        onClick={() => onValueChange(Math.max(min, value - 1))}
        disabled={value <= min}
      >
        -
      </Button>
      <span className="text-xl md:text-2xl font-bold text-gray-800 w-10 md:w-12 text-center">{value}</span>
      <Button
        variant="ghost"
        size="icon"
        className="w-10 h-10 rounded-full bg-white shadow-md text-lg text-gray-700 hover:bg-gray-50 disabled:opacity-50 transition-all duration-200 transform hover:scale-105"
        onClick={() => onValueChange(Math.min(max, value + 1))}
        disabled={value >= max}
      >
        +
      </Button>
    </div>
  </div>
);

const RoomsAndTravelers = ({ onContinue, onBack, initialRooms, initialAdults, initialChildren, selectedTheme, selectedThemeId, onThemeChange, bookingData, onNavigate }: { onContinue: (rooms: number, adults: number, children: number) => void; onBack: () => void; initialRooms: number; initialAdults: number; initialChildren: number; selectedTheme?: string; selectedThemeId?: string; onThemeChange?: (theme: string, themeId: string) => void; bookingData: BookingData; onNavigate: (step: number) => void }) => {
  const [adults, setAdults] = useState(initialAdults);
  const [children, setChildren] = useState(initialChildren);
  const [rooms, setRooms] = useState(initialRooms);
  const [focused, setFocused] = useState(selectedTheme || '');
  const [focusedId, setFocusedId] = useState(selectedThemeId || '');
  const { data: themes, isLoading: themesLoading } = useQuery<Interest[]>("fetch Interest", getInterest);

  // --- Start of logic from OptionsBox.tsx ---
  const [minRooms, setMinRooms] = useState(1);
  const [showChild, setShowChild] = useState(true);
  const [guests, setGuests] = useState(4);

  useEffect(() => {
    let r = 0;
    if (children == 0) {
      if (focused !== "Honeymoon") r = Math.ceil(adults / 3);
      else r = Math.ceil(adults / 2);
    } else {
      let x = adults;
      let y = children;
      r = 0;
      while (x >= 3 && y >= 1) {
        x = x - 3;
        y = y - 1;
        r++;
      }
      while (x > 0 || y > 0) {
        x = x - 3;
        y = y - 3;
        r++;
      }
    }
    setRooms(r);
    setMinRooms(r);
  }, [children, adults, focused]);

  useEffect(() => {
    switch (focused) {
      case "Couple": {
        setShowChild(true);
        setGuests(4);
        break;
      }
      case "Honeymoon": {
        setAdults(2)
        setChildren(0);
        setShowChild(false);
        setGuests(2);
        break;
      }
      default: {
        setShowChild(true);
        setGuests(4); // Default value
      }
    }
  }, [focused]);
  // --- End of logic from OptionsBox.tsx ---


  const handleThemeFocus = (theme: Interest) => {
    setFocused(theme.interestName);
    setFocusedId(theme.interestId);
    if (onThemeChange) {
      onThemeChange(theme.interestName, theme.interestId);
    }

    if (theme.interestName === 'Honeymoon' || theme.interestName === 'Couple') {
      setAdults(2);
      setChildren(0);
    } else if (theme.interestName === 'Family') {
      setAdults(2);
      setChildren(2);
    } else if (theme.interestName === 'Friends') {
      setAdults(4);
      setChildren(0);
    }
  };

  const handleAdultsChange = (newAdults: number) => {
    if (focused === "Honeymoon" && newAdults < 2) {
        return;
    }
    setAdults(newAdults);
  }

  const handleChildrenChange = (newChildren: number) => {
    if (!showChild) {
        setChildren(0);
        return;
    }
    setChildren(newChildren);
  }

  const handleRoomsChange = (i: boolean) => {
    if (i) { // increment
      if (rooms < adults) {
        setRooms(rooms + 1);
      }
    } else { // decrement
      if (!(rooms - 1 < minRooms)) {
        setRooms(rooms - 1);
      }
    }
  }

  return (
    <PageLayout
      currentStep={2}
      title="How many people are traveling?"
      backgroundImage="/1976998.jpg"
      leftButton={
        <Button variant="outline" className="w-full sm:w-auto sm:px-2 md:px-8 py-4 text-lg bg-white hover:bg-gray-50 border-2 border-gray-200 hover:border-gray-300 rounded-full shadow-md hover:shadow-lg transition-all duration-300 transform hover:scale-105" onClick={onBack}>
          <ArrowLeft className="w-5 h-5 md:mr-2" />
          <span className="hidden md:inline">Back</span>
        </Button>
      }
      bookingData={bookingData}
      onNavigate={onNavigate}
    >
      <div className="max-w-6xl mx-auto px-2 md:px-0">
        {/* Main Content Grid: Left - counters, Right - summary */}
        <div className="grid grid-cols-1 lg:grid-cols-5 gap-6 md:gap-12">
          {/* Left: Themes + Counters */}
          <div className="lg:col-span-3 space-y-6 md:space-y-8">
            <div className="bg-white/80 backdrop-blur-sm border border-gray-200/90 rounded-2xl p-4 md:p-6 shadow-lg hover:shadow-xl transition-shadow duration-300">
              <div className="flex items-center text-gray-700 mb-4 md:mb-6">
                <Sparkles className="w-5 h-5 md:w-6 md:h-6 mr-2 md:mr-3 text-[#ff7865]" />
                <h3 className="text-lg md:text-xl font-semibold">What type of trip are you planning?</h3>
              </div>
              {themesLoading ? (
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 md:gap-4">
                  {[...Array(8)].map((_, index) => (
                    <div key={index} className="w-full animate-pulse h-20 md:h-24 bg-slate-200 rounded-xl"></div>
                  ))}
                </div>
              ) : (
                <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
                  {themes?.map((theme) => {
                    const isSelected = focused === theme.interestName;
                    return (
                      <Card
                        key={theme._id}
                        className={cn(
                          'relative cursor-pointer transition-all duration-300 rounded-xl overflow-hidden group',
                          'transform hover:-translate-y-1 hover:shadow-lg',
                          isSelected 
                            ? 'ring-2 ring-[#ff7865] shadow-lg bg-gradient-to-br from-orange-100 via-red-100 to-pink-100 text-gray-800'
                            : 'bg-white border border-gray-200/80 hover:border-[#ff7865]/50',
                          'hover:ring-1 hover:ring-[#ff7865]/30'
                        )}
                        onClick={() => handleThemeFocus(theme)}
                      >
                        <CardContent className="p-3 sm:p-4 flex flex-col items-center justify-center h-24 sm:h-28 relative overflow-hidden">
                          {/* Theme icon with better loading state */}
                          <div className="relative h-8 w-8 sm:h-10 sm:w-10 mb-3 flex items-center justify-center">
                            {theme.image ? (
                              <img
                                src={
                                  `https://tripemilestone.in-maa-1.linodeobjects.com/${theme.image}`
                                }
                                alt={theme.interestName}
                                className={cn(
                                  'w-full h-full object-contain transition-transform duration-300',
                                  isSelected ? 'scale-110' : 'group-hover:scale-105'
                                )}
                                onError={(e) => {
                                  const target = e.target as HTMLImageElement;
                                  target.onerror = null;
                                  target.src = '/placeholder-theme.svg';
                                }}
                              />
                            ) : (
                              <div className="w-full h-full bg-gray-100 rounded-full flex items-center justify-center">
                                <Sparkles className="w-4 h-4 text-gray-400" />
                              </div>
                            )}
                          </div>
                          
                          {/* Theme name with better typography */}
                          <h3 className={cn(
                            'text-xs sm:text-sm font-semibold text-center transition-colors',
                            isSelected ? 'text-gray-800' : 'text-gray-800',
                            'w-full px-1'
                          )}>
                            {theme.interestName}
                          </h3>
                          
                          {/* Subtle hover effect */}
                          <div className={cn(
                            'absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-transparent',
                            'group-hover:from-white/5 group-hover:to-black/5',
                            'transition-all duration-300',
                            isSelected && 'opacity-0'
                          )}></div>
                        </CardContent>         
                      </Card>
                    );
                  })}
                </div>
              )}
            </div>

            <div className="flex flex-wrap md:grid md:grid-cols-3 gap-6 md:gap-8">
              <div className="w-[calc(50%-0.75rem)] md:w-auto">
                <Counter
                  label="Adults"
                  icon={UsersRoundIcon}
                  value={adults}
                  onValueChange={handleAdultsChange}
                  min={1} max={20}
                  selectedValue={`Above 12 yrs`} />
              </div>

              {showChild &&
                <div className="w-[calc(50%-0.75rem)] md:w-auto">
                  <Counter
                    label="Children"
                    icon={Users}
                    value={children}
                    onValueChange={handleChildrenChange}
                    min={0} max={10}
                    selectedValue={`5 to 11 yrs`} />
                </div>}

              <div className="w-full md:w-auto">
                <Counter
                  label="Rooms"
                  icon={Briefcase}
                  value={rooms}
                  onValueChange={(val) => setRooms(val)}
                  min={minRooms} max={adults} />
              </div>
            </div>
          </div>

          {/* Right: Summary */}
          <aside className="lg:col-span-2">
            <div className="bg-white/90 backdrop-blur-sm border-2 border-gray-100 rounded-3xl p-6 md:p-8 shadow-2xl sticky top-24">
              <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-6 text-center">Your Trip Summary</h3>
              <hr className="border-gray-200 mb-6" />
              
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
                  <div className="flex items-center text-gray-700">
                    <MapPin className="w-6 h-6 mr-4 text-[#ff7865]" />
                    <span className="text-lg">Destination</span>
                  </div>
                  <span className="font-bold text-lg text-gray-900 text-right">{bookingData.destination || 'Not Set'}</span>
                </div>

                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
                  <div className="flex items-center text-gray-700">
                    <CalendarIcon className="w-6 h-6 mr-4 text-[#ff7865]" />
                    <span className="text-lg">Date</span>
                  </div>
                  <span className="font-bold text-lg text-gray-900 text-right">{bookingData.date ? format(bookingData.date, 'PPP') : 'Not Set'}</span>
                </div>

                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
                  <div className="flex items-center text-gray-700">
                    <Users className="w-6 h-6 mr-4 text-[#ff7865]" />
                    <span className="text-lg">Travelers</span>
                  </div>
                  <span className="font-bold text-lg text-gray-900">{adults + children}</span>
                </div>

                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
                  <div className="flex items-center text-gray-700">
                    <Briefcase className="w-6 h-6 mr-4 text-[#ff7865]" />
                    <span className="text-lg">Rooms</span>
                  </div>
                  <span className="font-bold text-lg text-gray-900">{rooms}</span>
                </div>

                {focused && (
                  <div className="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
                    <div className="flex items-center text-gray-700">
                      <Sparkles className="w-6 h-6 mr-4 text-[#ff7865]" />
                      <span className="text-lg">Theme</span>
                    </div>
                    <span className="font-bold text-lg text-gray-900">{focused}</span>
                  </div>
                )}
              </div>

              <div className="mt-8">
                <Button
                  className="w-full py-4 text-lg md:py-6 md:text-2xl bg-gradient-to-r from-[#ff7865] to-[#f95d47] text-white rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 group"
                  onClick={() => onContinue(rooms, adults, children)}
                >
                  <span className="relative flex items-center justify-center font-bold">
                    Search Packages
                    <ArrowRight className="w-6 h-6 ml-3 transition-transform group-hover:translate-x-1.5" />
                  </span>
                </Button>
              </div>
            </div>
          </aside>
        </div>
      </div>
    </PageLayout>
  );
};

const BookingPage = () => {
  const router = useRouter();
  const dispatch = useDispatch();
  const [currentStep, setCurrentStep] = useState(0); // 0: Destination, 1: Date, 2: Rooms/Travelers
  const [bookingData, setBookingData] = useState<BookingData>({
    destination: '',
    destinationId: '',
    date: undefined,
    rooms: 1,
    adults: 1,
    children: 0,
    theme: '',
    themeId: '',
    departureCity: '',
    tripType: '',
    duration: '',
  });

  const handleNext = () => {
    setCurrentStep(prev => prev + 1);
  };

  const handleBack = () => {
    setCurrentStep(prev => prev - 1);
  };

  const handleDestinationSelect = (destination: string, destinationId: string) => {
    setBookingData(prev => ({ ...prev, destination, destinationId }));
    handleNext();
  };

  const handleThemeChange = (theme: string, themeId: string) => {
    setBookingData(prev => ({ ...prev, theme, themeId }));
  };

  const handleDateSelect = (date: Date) => {
    setBookingData(prev => ({ ...prev, date }));
    handleNext();
  };

  const handleRoomsTravelersSelect = (rooms: number, adults: number, children: number) => {
    // Save rooms and travelers
    setBookingData(prev => ({ ...prev, rooms, adults, children }));

    // Immediately finalize booking (no final page)
    const finalData = { ...bookingData, rooms, adults, children };

    // Dispatch destination and date to Redux store
    dispatch(changeDestination(finalData.destination));
    dispatch(changeDestinationId(finalData.destinationId));
    if (finalData.date) {
      dispatch(changeDate(finalData.date.toISOString()));
    }

    // Dispatch theme to Redux store
    dispatch(selectTheme({ selectedTheme: finalData.theme }));
    dispatch(selectThemeId({ selectedThemeId: finalData.themeId }));

    // Dispatch room and traveler data to Redux store
    dispatch(selectAdultsChild({
      room: {
        adult: finalData.adults,
        child: finalData.children,
        room: finalData.rooms,
      },
    }));

    // Navigate to packages page
    router.push("/packages");
  };

  const handleUpdateBookingData = (newData: Partial<BookingData>) => {
    setBookingData(prev => ({ ...prev, ...newData }));
  };

  const handleFinalBooking = () => {
    // Dispatch destination and date to Redux store
    dispatch(changeDestination(bookingData.destination));
    dispatch(changeDestinationId(bookingData.destinationId));
    dispatch(changeDate(bookingData.date?.toISOString()));
    
    // Dispatch theme to Redux store
    dispatch(selectTheme({ selectedTheme: bookingData.theme }));
    dispatch(selectThemeId({ selectedThemeId: bookingData.themeId }));
    
    // Dispatch room and traveler data to Redux store
    dispatch(selectAdultsChild({ 
      room: { 
        adult: bookingData.adults, 
        child: bookingData.children, 
        room: bookingData.rooms 
      } 
    }));
    
    // Navigate to packages page
    router.push("/packages");
  };

  const renderStep = () => {
    const navigateTo = (step: number) => setCurrentStep(step);
    switch (currentStep) {
      case 0:
        return (
          <DestinationSelection
            onContinue={handleDestinationSelect}
            bookingData={bookingData}
            onNavigate={navigateTo}
            setBookingData={setBookingData}
          />
        );
      case 1:
        return (
          <DepartureDate
            onContinue={handleDateSelect}
            onBack={handleBack}
            selectedDate={bookingData.date}
            bookingData={bookingData}
            onNavigate={navigateTo}
          />
        );
      case 2:
        return (
          <RoomsAndTravelers
            onContinue={handleRoomsTravelersSelect}
            onBack={handleBack}
            initialRooms={bookingData.rooms}
            initialAdults={bookingData.adults}
            initialChildren={bookingData.children}
            selectedTheme={bookingData.theme}
            selectedThemeId={bookingData.themeId}
            onThemeChange={handleThemeChange}
            bookingData={bookingData}
            onNavigate={navigateTo}
          />
        );
      case 3:
        return null;
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen">
      {renderStep()}
    </div>
  );
};

export default BookingPage;
