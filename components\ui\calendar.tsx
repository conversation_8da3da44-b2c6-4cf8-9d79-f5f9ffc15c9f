"use client"

import * as React from "react"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { DayPicker } from "react-day-picker"
import { getHoliday } from "@/lib/indian-holidays"
import { format } from 'date-fns';

import { cn } from "@/lib/utils"
import { buttonVariants } from "@/components/ui/button"

export type CalendarProps = React.ComponentProps<typeof DayPicker>

// Helper function to format a date as 'YYYY-MM-DD'
const formatDate = (date: Date): string => {
  const y = date.getFullYear();
  const m = String(date.getMonth() + 1).padStart(2, '0');
  const d = String(date.getDate()).padStart(2, '0');
  return `${y}-${m}-${d}`;
}

const DayContent = ({ date, displayMonth }: { date: Date, displayMonth: Date }) => {
  const holiday = getHoliday(date);
  const isOutside = date.getMonth() !== displayMonth.getMonth();
  const isCurrentMonthHoliday = holiday && !isOutside;
  return (
    <div className={cn(
      "relative w-full h-full flex flex-col items-center justify-center",
      isCurrentMonthHoliday && "border border-red-400 rounded-t-2xl rounded-b-2xl md:pt-3.5"
    )}>
      <time dateTime={format(date, "yyyy-MM-dd")}>{format(date, "d")}</time>
      {isCurrentMonthHoliday && (
        <span className="absolute top-0 text-[8px] bg-red-400 text-white font-semibold  truncate w-full px-1 text-center rounded-t-2xl hidden md:block">
          {holiday.name}
        </span>
      )}
    </div>
  );
};


function Calendar({
  className,
  classNames,
  showOutsideDays = true,
  ...props
}: CalendarProps) {
  // 1. Keep track of the currently displayed month
  const [month, setMonth] = React.useState<Date>(props.defaultMonth || new Date());

  const isHoliday = (date: Date): boolean => {
    return getHoliday(date) !== null;
  };

  return (
    <div className="relative w-full">
      {/* --- Mobile View --- */}
      <div className="md:hidden">
        <DayPicker
          showOutsideDays={showOutsideDays}
          className={cn(className, 'w-full')}
          numberOfMonths={12}
          pagedNavigation={false}
          disableNavigation
          modifiers={{ holiday: isHoliday }}
          classNames={{
            months: "flex flex-col w-full h-[800px] overflow-y-auto pb-8 px-2 snap-y snap-mandatory",
            month: "w-full space-y-1 p-2 flex-shrink-0 bg-white rounded-lg shadow-sm snap-start",
            caption: "flex justify-center items-center relative mb-3 bg-white py-2 sticky top-0 z-10",
            caption_label: "text-base font-semibold text-gray-800 text-center px-4 py-1 bg-white rounded-full",
            nav: "hidden",
            nav_button: cn(
              buttonVariants({ variant: "ghost" }),
              "h-8 w-8 p-0 rounded-full flex items-center justify-center hover:bg-gray-100 text-gray-600"
            ),
            nav_button_previous: "hidden",
            nav_button_next: "hidden",
            table: "w-full border-collapse",
            head_row: "flex justify-between w-full",
            head_cell: "text-muted-foreground rounded-md w-[calc(100%/7)] text-center font-normal text-[0.8rem]",
            row: "flex w-full justify-between",
            cell: "h-20 w-[calc(100%/7)] text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",
            day: cn("h-20 w-20 mx-auto rounded p-0 font-normal aria-selected:opacity-100"),
            day_range_end: "day-range-end",
            day_selected: "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",
            day_today: "bg-accent text-accent-foreground",
            day_outside: "day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30",
            day_disabled: "text-muted-foreground opacity-50",
            day_range_middle: "aria-selected:bg-accent aria-selected:text-accent-foreground",
            day_hidden: "invisible",
            ...classNames,
          }}
          components={{
            DayContent: (props) => (
              <DayContent date={props.date} displayMonth={props.displayMonth} />
            ),
            IconLeft: () => <ChevronLeft className="h-4 w-4" />,
            IconRight: () => <ChevronRight className="h-4 w-4" />,
          }}
          {...props}
        />
      </div>

      {/* --- Desktop View --- */}
      <div className="hidden md:flex justify-center w-full">
        <div className="w-[900px]">
          <DayPicker
            showOutsideDays={showOutsideDays}
            className={cn(className, 'w-full')}
            numberOfMonths={1}
            month={month}             // Control the displayed month
            onMonthChange={setMonth}    // Update state when user navigates
            modifiers={{ holiday: isHoliday }}
            classNames={{
            months: "w-full",
            month: "w-full space-y-1 bg-white rounded-lg shadow-sm p-4",
            caption: "flex items-center justify-center mb-4 relative h-10",
            caption_label: "text-lg font-semibold text-gray-800 text-center absolute left-0 right-0",
            nav: "flex items-center justify-between w-full h-full absolute left-0 px-4",
            nav_button: cn(
              buttonVariants({ variant: "ghost" }),
              "h-8 w-8 p-0 rounded-full flex items-center justify-center hover:bg-gray-100 text-gray-600"
            ),
            nav_button_previous: "",
            nav_button_next: "",
            table: "w-full border-collapse",
            head_row: "flex justify-between w-full",
            head_cell: "text-muted-foreground rounded-md w-[calc(100%/7)] text-center font-normal text-[0.8rem]",
            row: "flex w-full justify-between",
            cell: "h-20 w-[calc(100%/7)] text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",
            day: cn("h-20 w-20 mx-auto rounded p-0 font-normal aria-selected:opacity-100"),
            day_range_end: "day-range-end",
            day_selected: "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",
            day_today: "bg-accent text-accent-foreground",
            day_outside: "day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30",
            day_disabled: "text-muted-foreground opacity-50",
            day_range_middle: "aria-selected:bg-accent aria-selected:text-accent-foreground",
            day_hidden: "invisible",
              ...classNames,
            }}
            components={{
              DayContent: (props) => (
                <DayContent date={props.date} displayMonth={props.displayMonth} />
              ),
              IconLeft: () => <ChevronLeft className="h-4 w-4" />,
              IconRight: () => <ChevronRight className="h-4 w-4" />,
            }}
            {...props}
          />
        </div>
      </div>
    </div>
  )
}
Calendar.displayName = "Calendar"

export { Calendar }
